# Variant Types Table Documentation

## Table Overview

The `variant_types` table stores the available types of variants that can be used across products. It includes both predefined system variant types (like size, color, material) and custom variant types created by users. This table serves as a reference for creating product variants.

## Table Schema

| Column Name | Data Type | Nullable | Default | Description |
|-------------|-----------|----------|---------|-------------|
| id | uuid | NO | gen_random_uuid() | Primary key for the variant type record |
| name | text | NO | | Unique identifier for the variant type (lowercase, no spaces) |
| display_name | text | NO | | Human-readable name for the variant type |
| description | text | YES | | Optional description of the variant type |
| is_predefined | boolean | NO | true | Whether this is a system-predefined variant type |
| sort_order | integer | NO | 0 | Order for displaying variant types in UI |
| created_at | timestamptz | NO | now() | Timestamp when the record was created |
| updated_at | timestamptz | NO | now() | Timestamp when the record was last updated |

## Constraints

### Primary Key
- `variant_types_pkey` - Primary key constraint on the `id` column

### Unique Constraints
- `variant_types_name_key` - Ensures variant type names are unique

### Check Constraints
- `variant_types_name_not_empty` - Ensures name is not empty
- `variant_types_display_name_not_empty` - Ensures display_name is not empty

## Indexes

| Index Name | Type | Columns | Description |
|------------|------|---------|-------------|
| variant_types_pkey | UNIQUE | id | Primary key index |
| variant_types_name_key | UNIQUE | name | Unique constraint index for name |
| idx_variant_types_name | BTREE | name | Index for name lookups |
| idx_variant_types_is_predefined | BTREE | is_predefined | Index for filtering predefined types |
| idx_variant_types_sort_order | BTREE | sort_order | Index for ordered display |

## Triggers

### update_variant_types_updated_at
- **Events**: UPDATE
- **Function**: update_updated_at_column()
- **Description**: Automatically updates the updated_at timestamp when a record is modified

## Row Level Security (RLS) Policies

| Policy Name | Command | Description |
|-------------|---------|-------------|
| Allow public read access to variant types | SELECT | Everyone can read variant types (reference data) |
| Allow authenticated users to create custom variant types | INSERT | Authenticated users can create custom variant types |
| Allow users to update their custom variant types | UPDATE | Users can update their own custom variant types |
| Prevent deletion of predefined variant types | DELETE | Only custom variant types can be deleted |

## Predefined Variant Types

The system comes with the following predefined variant types:

| Name | Display Name | Description | Sort Order |
|------|--------------|-------------|------------|
| size | Size | Product size variations (S, M, L, XL, etc.) | 1 |
| color | Color | Product color variations | 2 |
| material | Material | Product material variations (Cotton, Silk, Leather, etc.) | 3 |
| flavor | Flavor | Product flavor variations for food items | 4 |
| capacity | Capacity | Storage or volume capacity variations | 5 |
| duration | Duration | Service duration variations | 6 |
| package | Package | Service package variations | 7 |
| style | Style | Product style variations | 8 |
| pattern | Pattern | Product pattern variations | 9 |
| finish | Finish | Product finish variations (Matte, Glossy, etc.) | 10 |
| weight | Weight | Product weight variations | 11 |
| dimensions | Dimensions | Product dimension variations | 12 |

## Usage Examples

### Querying All Variant Types
```sql
SELECT * FROM variant_types 
ORDER BY sort_order, display_name;
```

### Querying Only Predefined Types
```sql
SELECT * FROM variant_types 
WHERE is_predefined = true 
ORDER BY sort_order;
```

### Creating a Custom Variant Type
```sql
INSERT INTO variant_types (
    name, 
    display_name, 
    description, 
    is_predefined, 
    sort_order
) VALUES (
    'texture',
    'Texture',
    'Surface texture variations for products',
    false,
    13
);
```

## Related Tables

### variant_options
Contains the possible values for each variant type (e.g., S, M, L for size).

### product_variants
Uses variant types in the variant_values JSONB column to define specific variant combinations.

## Business Rules

1. **Uniqueness**: Variant type names must be unique across the system
2. **Predefined Protection**: Predefined variant types cannot be deleted or have their core properties modified
3. **Custom Types**: Users can create custom variant types for specific business needs
4. **Naming Convention**: Names should be lowercase with no spaces for consistency
5. **Display Order**: Sort order determines the sequence in UI components

## API Integration

This table is commonly used in:
- Variant type selector components (combobox/dropdown)
- Product variant creation forms
- Variant filtering and search functionality
- Admin panels for managing variant types

## Performance Considerations

1. **Small Dataset**: This table typically contains a small number of records (< 100)
2. **Read-Heavy**: Mostly read operations with occasional custom type creation
3. **Caching**: Good candidate for application-level caching due to infrequent changes
4. **Indexing**: Optimized for name lookups and ordered display
