// TypeScript types for product variants functionality

// Database table types (matching the actual database schema)
export interface ProductVariant {
  id: string;
  product_id: string;
  variant_name: string;
  variant_values: Record<string, string>; // JSONB object with variant type-value pairs
  base_price?: number | null;
  discounted_price?: number | null;
  is_available: boolean;
  images: string[];
  featured_image_index: number;
  created_at: string;
  updated_at: string;
}

export interface VariantType {
  id: string;
  name: string; // Unique identifier (lowercase, no spaces)
  display_name: string; // Human-readable name
  description?: string | null;
  is_predefined: boolean;
  sort_order: number;
  created_at: string;
  updated_at: string;
}

export interface VariantOption {
  id: string;
  variant_type_id: string;
  value: string; // Actual value (lowercase, no spaces)
  display_value: string; // Human-readable display value
  color_code?: string | null; // Hex color code for color variants
  is_predefined: boolean;
  sort_order: number;
  created_at: string;
  updated_at: string;
}

// Extended types with relationships
export interface VariantTypeWithOptions extends VariantType {
  options: VariantOption[];
}

export interface ProductWithVariants {
  id: string;
  business_id: string;
  name: string;
  description?: string | null;
  base_price?: number | null;
  discounted_price?: number | null;
  is_available: boolean;
  images: string[];
  featured_image_index: number;
  variant_count: number;
  variants: ProductVariant[];
}

// Form types for creating/editing variants
export interface CreateVariantData {
  product_id: string;
  variant_name: string;
  variant_values: Record<string, string>;
  base_price?: number | null;
  discounted_price?: number | null;
  is_available?: boolean;
  images?: string[];
  featured_image_index?: number;
}

export interface UpdateVariantData extends Partial<CreateVariantData> {
  id: string;
}

// Variant combination generation types
export interface VariantCombination {
  combination: Record<string, string>;
  combination_name: string;
}

export interface VariantGenerationInput {
  product_id: string;
  variant_types_values: Record<string, string[]>;
}

// UI component types
export interface VariantSelectorProps {
  variants: ProductVariant[];
  selectedVariant?: ProductVariant | null;
  onVariantSelect: (variant: ProductVariant) => void;
  className?: string;
}

export interface VariantFormData {
  variant_name: string;
  variant_values: Record<string, string>;
  base_price?: number;
  discounted_price?: number;
  is_available: boolean;
  images: File[];
  featured_image_index: number;
}

// Business statistics types
export interface VariantStats {
  total_products: number;
  products_with_variants: number;
  total_variants: number;
  available_variants: number;
}

// API response types
export interface VariantApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

export interface GetProductWithVariantsResponse {
  product_id: string;
  product_name: string;
  product_description?: string;
  product_base_price?: number;
  product_discounted_price?: number;
  product_is_available: boolean;
  product_images: string[];
  product_featured_image_index: number;
  variant_count: number;
  variants: ProductVariant[];
}

// Predefined variant types (matching database seeded data)
export const PREDEFINED_VARIANT_TYPES = [
  'size',
  'color',
  'material',
  'flavor',
  'capacity',
  'duration',
  'package',
  'style',
  'pattern',
  'finish',
  'weight',
  'dimensions'
] as const;

export type PredefinedVariantType = typeof PREDEFINED_VARIANT_TYPES[number];

// Validation constants
export const VARIANT_CONSTRAINTS = {
  MAX_VARIANTS_PER_PRODUCT: 100,
  MAX_VARIANT_TYPES_PER_PRODUCT: 5,
  MAX_VARIANT_NAME_LENGTH: 100,
  MAX_IMAGES_PER_VARIANT: 5,
} as const;

// Utility types for variant operations
export type VariantOperation = 'create' | 'update' | 'delete';

export interface VariantOperationResult {
  success: boolean;
  variant?: ProductVariant;
  error?: string;
}

// Filter and sort types for variant management
export interface VariantFilters {
  is_available?: boolean;
  variant_type?: string;
  search_term?: string;
}

export type VariantSortBy = 
  | 'created_asc'
  | 'created_desc'
  | 'name_asc'
  | 'name_desc'
  | 'price_asc'
  | 'price_desc';

// Bulk operations types
export interface BulkVariantOperation {
  variant_ids: string[];
  operation: 'enable' | 'disable' | 'delete' | 'update_price';
  data?: {
    is_available?: boolean;
    base_price?: number;
    discounted_price?: number;
  };
}

export interface BulkVariantResult {
  success: boolean;
  updated_count: number;
  failed_count: number;
  errors?: string[];
}
