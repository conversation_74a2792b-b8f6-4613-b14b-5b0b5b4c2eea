# Variant Options Table Documentation

## Table Overview

The `variant_options` table stores the possible values for each variant type. For example, for the "size" variant type, this table would contain options like "S", "M", "L", "XL". It includes both predefined system options and custom options created by users.

## Table Schema

| Column Name | Data Type | Nullable | Default | Description |
|-------------|-----------|----------|---------|-------------|
| id | uuid | NO | gen_random_uuid() | Primary key for the variant option record |
| variant_type_id | uuid | NO | | Foreign key to variant_types.id |
| value | text | NO | | The actual value stored (lowercase, no spaces) |
| display_value | text | NO | | Human-readable display value |
| color_code | text | YES | | Hex color code for color variants (#RRGGBB format) |
| is_predefined | boolean | NO | true | Whether this is a system-predefined option |
| sort_order | integer | NO | 0 | Order for displaying options in UI |
| created_at | timestamptz | NO | now() | Timestamp when the record was created |
| updated_at | timestamptz | NO | now() | Timestamp when the record was last updated |

## Constraints

### Primary Key
- `variant_options_pkey` - Primary key constraint on the `id` column

### Foreign Keys
- `variant_options_variant_type_id_fkey` - Foreign key constraint linking `variant_type_id` to `variant_types.id` with CASCADE DELETE

### Unique Constraints
- `variant_options_variant_type_id_value_key` - Ensures unique value per variant type

### Check Constraints
- `variant_options_value_not_empty` - Ensures value is not empty
- `variant_options_display_value_not_empty` - Ensures display_value is not empty
- `variant_options_color_code_format` - Validates hex color code format (#RRGGBB)

## Indexes

| Index Name | Type | Columns | Description |
|------------|------|---------|-------------|
| variant_options_pkey | UNIQUE | id | Primary key index |
| variant_options_variant_type_id_value_key | UNIQUE | variant_type_id, value | Unique constraint index |
| idx_variant_options_variant_type_id | BTREE | variant_type_id | Index for variant type lookups |
| idx_variant_options_value | BTREE | value | Index for value searches |
| idx_variant_options_is_predefined | BTREE | is_predefined | Index for filtering predefined options |
| idx_variant_options_sort_order | BTREE | sort_order | Index for ordered display |
| idx_variant_options_type_predefined | BTREE | variant_type_id, is_predefined | Composite index for common queries |
| idx_variant_options_type_sort | BTREE | variant_type_id, sort_order | Composite index for ordered type options |

## Triggers

### update_variant_options_updated_at
- **Events**: UPDATE
- **Function**: update_updated_at_column()
- **Description**: Automatically updates the updated_at timestamp when a record is modified

## Row Level Security (RLS) Policies

| Policy Name | Command | Description |
|-------------|---------|-------------|
| Allow public read access to variant options | SELECT | Everyone can read variant options (reference data) |
| Allow authenticated users to create custom variant options | INSERT | Authenticated users can create custom options |
| Allow users to update their custom variant options | UPDATE | Users can update their own custom options |
| Prevent deletion of predefined variant options | DELETE | Only custom options can be deleted |

## Predefined Options

### Size Options
| Value | Display Value |
|-------|---------------|
| xs | XS |
| s | S |
| m | M |
| l | L |
| xl | XL |
| xxl | XXL |
| xxxl | XXXL |

### Color Options
| Value | Display Value | Color Code |
|-------|---------------|------------|
| red | Red | #FF0000 |
| blue | Blue | #0000FF |
| green | Green | #008000 |
| black | Black | #000000 |
| white | White | #FFFFFF |
| yellow | Yellow | #FFFF00 |
| orange | Orange | #FFA500 |
| purple | Purple | #800080 |
| pink | Pink | #FFC0CB |
| brown | Brown | #A52A2A |
| gray | Gray | #808080 |
| navy | Navy | #000080 |

## Usage Examples

### Querying Options for a Variant Type
```sql
SELECT vo.* 
FROM variant_options vo
JOIN variant_types vt ON vo.variant_type_id = vt.id
WHERE vt.name = 'size'
ORDER BY vo.sort_order;
```

### Querying Color Options with Codes
```sql
SELECT vo.* 
FROM variant_options vo
JOIN variant_types vt ON vo.variant_type_id = vt.id
WHERE vt.name = 'color'
AND vo.color_code IS NOT NULL
ORDER BY vo.sort_order;
```

### Creating a Custom Option
```sql
INSERT INTO variant_options (
    variant_type_id,
    value,
    display_value,
    is_predefined,
    sort_order
) VALUES (
    (SELECT id FROM variant_types WHERE name = 'size'),
    'xxxxl',
    'XXXXL',
    false,
    8
);
```

### Creating a Custom Color Option
```sql
INSERT INTO variant_options (
    variant_type_id,
    value,
    display_value,
    color_code,
    is_predefined,
    sort_order
) VALUES (
    (SELECT id FROM variant_types WHERE name = 'color'),
    'turquoise',
    'Turquoise',
    '#40E0D0',
    false,
    13
);
```

## Related Tables

### variant_types
Parent table containing the variant type definitions.

### product_variants
Uses these options in the variant_values JSONB column to define specific variant combinations.

## Business Rules

1. **Uniqueness**: Option values must be unique within each variant type
2. **Predefined Protection**: Predefined options cannot be deleted
3. **Color Codes**: Only color variant types should have color_code values
4. **Value Format**: Values should be lowercase with no spaces for consistency
5. **Display Values**: Should be human-readable and properly formatted

## Special Features

### Color Code Validation
- Must be in hex format: #RRGGBB
- Only applicable to color variant types
- Used for displaying color swatches in UI

### Sort Order
- Determines display sequence in dropdowns and selectors
- Predefined options have logical ordering (XS, S, M, L, XL...)
- Custom options can be inserted at appropriate positions

## API Integration

This table is commonly used in:
- Variant option selectors (dropdowns, chips, buttons)
- Product variant creation forms
- Color picker components
- Size selection interfaces
- Admin panels for managing options

## Performance Considerations

1. **Moderate Dataset**: Typically contains hundreds of records
2. **Read-Heavy**: Mostly read operations with occasional custom option creation
3. **Caching**: Good candidate for application-level caching
4. **Indexing**: Optimized for variant type lookups and ordered display
5. **Joins**: Frequently joined with variant_types for complete information
