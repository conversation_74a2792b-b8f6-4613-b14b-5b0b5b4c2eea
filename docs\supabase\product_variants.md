# Product Variants Table Documentation

## Table Overview

The `product_variants` table stores individual variants of products, allowing businesses to offer multiple variations of a single product with different attributes, pricing, and images. This table supports the product variants functionality that enables businesses to create variations like size, color, material, etc., without duplicating entire product listings.

## Table Schema

| Column Name | Data Type | Nullable | Default | Description |
|-------------|-----------|----------|---------|-------------|
| id | uuid | NO | gen_random_uuid() | Primary key for the product variant record |
| product_id | uuid | NO | | Foreign key to products_services.id |
| variant_name | text | NO | | Human-readable name for the variant combination |
| variant_values | jsonb | NO | '{}' | JSON object storing variant type-value pairs |
| base_price | numeric | YES | | Original price of the product variant |
| discounted_price | numeric | YES | | Discounted/sale price of the product variant |
| is_available | boolean | NO | true | Flag indicating whether the variant is currently available |
| images | text[] | YES | '{}' | Array of image URLs specific to this variant |
| featured_image_index | integer | YES | 0 | Index of the featured image in the images array |
| created_at | timestamptz | NO | now() | Timestamp when the record was created |
| updated_at | timestamptz | NO | now() | Timestamp when the record was last updated |

## Constraints

### Primary Key
- `product_variants_pkey` - Primary key constraint on the `id` column

### Foreign Keys
- `product_variants_product_id_fkey` - Foreign key constraint linking `product_id` to `products_services.id` with CASCADE DELETE

### Check Constraints
- `product_variants_base_price_positive` - Ensures base_price is non-negative
- `product_variants_discounted_price_positive` - Ensures discounted_price is non-negative
- `product_variants_discounted_less_than_base` - Ensures discounted_price ≤ base_price
- `product_variants_featured_image_index_valid` - Ensures featured_image_index ≥ 0
- `product_variants_variant_name_not_empty` - Ensures variant_name is not empty

## Indexes

| Index Name | Type | Columns | Description |
|------------|------|---------|-------------|
| product_variants_pkey | UNIQUE | id | Primary key index |
| idx_product_variants_product_id | BTREE | product_id | Index for product lookups |
| idx_product_variants_is_available | BTREE | is_available | Index for availability filtering |
| idx_product_variants_created_at | BTREE | created_at | Index for chronological ordering |
| idx_product_variants_product_available | BTREE | product_id, is_available | Composite index for common queries |
| idx_product_variants_product_created | BTREE | product_id, created_at DESC | Composite index for product variants by date |
| idx_product_variants_variant_values_gin | GIN | variant_values | GIN index for JSONB queries |
| idx_product_variants_count | BTREE | product_id WHERE is_available = true | Partial index for counting available variants |

## Triggers

### update_product_variants_updated_at
- **Events**: UPDATE
- **Function**: update_updated_at_column()
- **Description**: Automatically updates the updated_at timestamp when a record is modified

### validate_product_variant_trigger
- **Events**: INSERT, UPDATE
- **Function**: validate_product_variant()
- **Description**: Validates variant constraints including max variants per product and duplicate combinations

### check_product_variant_availability_trigger
- **Events**: UPDATE
- **Function**: check_product_variant_availability()
- **Description**: Ensures at least one variant remains available if product has variants

## Row Level Security (RLS) Policies

| Policy Name | Command | Description |
|-------------|---------|-------------|
| Allow business owners to manage their product variants | ALL | Business owners can manage variants for their own products |
| Allow public read access for variants of online business products | SELECT | Public can view available variants from online businesses |

## Variant Values Structure

The `variant_values` column stores a JSON object with variant type-value pairs:

```json
{
  "size": "Large",
  "color": "Red",
  "material": "Cotton"
}
```

### Constraints on variant_values:
- Cannot be empty
- Maximum of 5 variant types per product
- Must contain valid key-value pairs

## Usage Examples

### Creating a Variant
```sql
INSERT INTO product_variants (
    product_id, 
    variant_name, 
    variant_values, 
    base_price, 
    discounted_price
) VALUES (
    'product-uuid-here',
    'Large Red Cotton',
    '{"size": "Large", "color": "Red", "material": "Cotton"}',
    29.99,
    24.99
);
```

### Querying Variants by Type
```sql
SELECT * FROM product_variants 
WHERE variant_values->>'size' = 'Large'
AND product_id = 'product-uuid-here';
```

## Related Tables

### products_services
The parent table containing the main product information. Each variant belongs to one product.

### variant_types
Reference table containing available variant types (size, color, etc.).

### variant_options
Reference table containing possible values for each variant type.

## Business Rules

1. **Maximum Variants**: Each product can have up to 100 variants
2. **Unique Combinations**: No duplicate variant combinations allowed within a product
3. **Availability**: At least one variant must be available if a product has variants
4. **Variant Types**: Maximum of 5 variant types per product
5. **Pricing**: Discounted price must be less than or equal to base price
6. **Images**: Variants can have their own images or fall back to product images

## Performance Considerations

1. **Indexing**: GIN index on variant_values enables efficient JSONB queries
2. **Partial Index**: Specialized index for counting available variants
3. **Composite Indexes**: Optimized for common query patterns
4. **Cascade Deletes**: Variants are automatically deleted when parent product is deleted
